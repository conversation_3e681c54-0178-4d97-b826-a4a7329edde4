<template>
	<div v-if="userProfileStore.isSuperAdmin">
		<div class="payment-agreement" v-if="data">
			代付款协议状态：
			<span :class="['status', data.signed ? 'success' : '']">{{ data.signed ? '已签署' : '未签署' }}</span>
			<span v-if="data.signed && data.expirationTime">（有效期至{{ formatExpirationTime }}）</span>
			<template v-if="data.signed">
				<a href="javascript:;" @click="onView">查看</a>
				<a href="javascript:;" @click="onClick(true)">修改</a>
			</template>
			<template v-else>
				<a href="javascript:;" @click="onClick(false)">去签署</a>
			</template>
			<span class="tip" v-if="!data.signed">{{ tip }}</span>
		</div>
		<template v-if="loading">
			<m-spin :spinning="true">
				<span class="loading">获取待付款协议信息中...</span>
			</m-spin>
		</template>

		<payment-agreement-dialog-comp ref="paymentAgreementDialogRef" />
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
