.invoice-order-selector {
	.order-select-item {
		margin-bottom: 16px;

		.order-select-row {
			display: flex;
			align-items: flex-start;
			gap: 8px;

			.order-select-wrapper {
				flex: 1;

				.order-select {
					width: 100%;
				}
			}

			.action-buttons {
				display: flex;
				align-items: center;
				height: 32px;
				padding-top: 4px;

				.remove-btn {
					color: #ff4d4f;
					border: none;
					padding: 4px 8px;
					height: auto;
					font-size: 12px;

					&:hover {
						color: #ff7875;
						background-color: #fff2f0;
					}
				}
			}
		}

		&:last-child {
			margin-bottom: 0;
		}
	}

	.add-button-wrapper {
		margin-top: 16px;

		.add-btn {
			width: 100%;
			height: 40px;
			border: 1px dashed #d9d9d9;
			border-radius: 6px;
			color: #666;
			background-color: #fafafa;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 4px;

			&:hover {
				border-color: #1890ff;
				color: #1890ff;
				background-color: #f0f8ff;
			}

			&:focus {
				border-color: #1890ff;
				color: #1890ff;
			}
		}
	}
}

// 响应式设计
@media (max-width: 768px) {
	.invoice-order-selector {
		.order-select-item {
			.order-select-row {
				flex-direction: column;
				gap: 8px;

				.action-buttons {
					align-self: flex-end;
					height: auto;
					padding-top: 0;
				}
			}
		}
	}
}
