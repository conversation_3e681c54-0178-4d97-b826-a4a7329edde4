# InvoiceOrderSelector 开票订单选择器组件

## 功能描述

根据原型设计，创建了一个可以动态添加和删除开票订单下拉框的组件，支持多个订单的选择和表单验证。

## 主要特性

1. **动态添加/删除**：支持动态添加和删除开票订单下拉框
2. **表单验证**：每个下拉框都有独立的表单校验
3. **金额计算**：自动计算总开票金额
4. **数据同步**：与父组件实时同步数据变化

## 组件结构

```
InvoiceOrderSelector/
├── index.vue          # 组件模板
├── index.ts           # 组件逻辑
├── index.less         # 组件样式
├── types.ts           # 类型定义
├── main.ts            # 入口文件
└── README.md          # 说明文档
```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| historyOrderList | HistoryOrderResponse[] | [] | 历史订单列表 |
| isOldOrder | boolean | false | 是否为老订单模式 |
| rules | object | {} | 表单验证规则 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | OrderSelectItem[] | 订单选择变化事件 |
| amount-change | number | 订单金额变化事件 |

## 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getSelectedOrders | - | OrderSelectItem[] | 获取选中的订单列表 |
| reset | - | void | 重置组件状态 |
| setOrderSelectList | OrderSelectItem[] | void | 设置订单选择列表 |

## 使用示例

```vue
<template>
  <invoice-order-selector
    ref="orderSelectorRef"
    :history-order-list="historyOrderList"
    :is-old-order="isOldOrder"
    @change="onOrderSelectChange"
    @amount-change="onTotalAmountChange"
  />
</template>

<script setup>
import InvoiceOrderSelector from './InvoiceOrderSelector/index.vue';

const orderSelectorRef = ref();

const onOrderSelectChange = (orderSelectList) => {
  console.log('订单选择变化:', orderSelectList);
};

const onTotalAmountChange = (totalAmount) => {
  console.log('总金额变化:', totalAmount);
};

// 获取选中的订单
const getSelectedOrders = () => {
  return orderSelectorRef.value?.getSelectedOrders() || [];
};
</script>
```

## 修改说明

### 新增文件
- `src/application/invoice-management/components/InvoiceOrderSelector/` - 新增组件目录及相关文件

### 修改文件
- `src/application/invoice-management/components/Invoice-application-dialog/index.vue` - 替换原有单个下拉框为新组件
- `src/application/invoice-management/components/Invoice-application-dialog/index.ts` - 更新组件逻辑以支持多订单选择
- `src/application/invoice-management/components/Invoice-application-dialog/types.ts` - 添加订单选择列表类型定义

### 主要改动
1. 将原有的单个订单选择下拉框替换为可动态添加/删除的组件
2. 支持多个订单同时选择和开票
3. 每个下拉框都有独立的表单验证
4. 自动计算总开票金额
5. 优化了用户交互体验

## 注意事项

1. 组件依赖 `@paas/paas-library` 中的 `MUtils` 工具类
2. 使用了 Ant Design Vue 3.x 的组件，前缀为 `m-`
3. 表单验证规则需要在父组件中配置
4. 组件支持响应式设计，在移动端会自动调整布局
