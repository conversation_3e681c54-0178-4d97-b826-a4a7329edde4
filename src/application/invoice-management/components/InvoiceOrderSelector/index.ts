import { computed, defineComponent, reactive, toRefs, watch, type PropType } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { MUtils } from '@paas/paas-library';
import { timeFilter } from '@/utils/utils';
import type {
	HistoryOrderResponse,
	InvoiceOrderSelectorState,
	OrderSelectItem
} from './types';

export default defineComponent({
	name: 'InvoiceOrderSelector',
	components: {
		PlusOutlined
	},
	props: {
		/** 历史订单列表 */
		historyOrderList: {
			type: Array as PropType<HistoryOrderResponse[]>,
			default: () => []
		},
		/** 是否为老订单模式 */
		isOldOrder: {
			type: Boolean,
			default: false
		},
		/** 表单验证规则 */
		rules: {
			type: Object,
			default: () => ({})
		}
	},
	emits: ['change', 'amount-change'],
	setup(props, { emit }) {
		const state = reactive<InvoiceOrderSelectorState>({
			orderSelectList: [
				{
					id: MUtils.generateUUID(),
					selectedOrderNo: undefined,
					invoiceAmount: 0
				}
			]
		});

		// 计算属性
		const computeds = {
			/** 格式化的历史订单列表 */
			formatHistoryOrderList: computed(() => {
				return props.historyOrderList
					.filter(item => item.enableApplyBlueInvoice)
					.map(item => ({
						...item,
						formatName: `${timeFilter(item.paidTime, 'YYYY年MM月DD日')}购买${item.goodsName}${
							item.orderAmount
						}元`
					}));
			}),

			/** 订单选择验证规则 */
			orderSelectRules: computed(() => [
				{
					required: props.isOldOrder,
					validator(rule: any, value: string) {
						if (!props.isOldOrder) {
							return Promise.resolve();
						}
						if (!value) {
							return Promise.reject('请选择开票订单');
						}
						return Promise.resolve();
					}
				}
			]),

			/** 总开票金额 */
			totalInvoiceAmount: computed(() => {
				return state.orderSelectList.reduce((total, item) => total + item.invoiceAmount, 0);
			})
		};

		// 方法
		const methods = {
			/**
			 * 添加订单选择项
			 */
			addOrderSelect() {
				const newItem: OrderSelectItem = {
					id: MUtils.generateUUID(),
					selectedOrderNo: undefined,
					invoiceAmount: 0
				};
				state.orderSelectList.push(newItem);
				methods.emitChange();
			},

			/**
			 * 删除订单选择项
			 * @param index 索引
			 */
			removeOrderSelect(index: number) {
				if (state.orderSelectList.length > 1) {
					state.orderSelectList.splice(index, 1);
					methods.emitChange();
					methods.emitAmountChange();
				}
			},

			/**
			 * 订单选择变化处理
			 * @param value 选中的订单号
			 * @param option 选中的订单选项
			 * @param index 索引
			 */
			onChangeOrder(value: string, option: HistoryOrderResponse, index: number) {
				const item = state.orderSelectList[index];
				if (item) {
					item.selectedOrderNo = value;
					item.invoiceAmount = option?.invoiceableAmount || 0;
				}
				methods.emitChange();
				methods.emitAmountChange();
			},

			/**
			 * 发送变化事件
			 */
			emitChange() {
				emit('change', [...state.orderSelectList]);
			},

			/**
			 * 发送金额变化事件
			 */
			emitAmountChange() {
				emit('amount-change', computeds.totalInvoiceAmount.value);
			},

			/**
			 * 获取选中的订单列表
			 */
			getSelectedOrders(): OrderSelectItem[] {
				return state.orderSelectList.filter(item => item.selectedOrderNo);
			},

			/**
			 * 重置组件状态
			 */
			reset() {
				state.orderSelectList = [
					{
						id: MUtils.generateUUID(),
						selectedOrderNo: undefined,
						invoiceAmount: 0
					}
				];
				methods.emitChange();
				methods.emitAmountChange();
			},

			/**
			 * 设置订单选择列表
			 * @param orders 订单列表
			 */
			setOrderSelectList(orders: OrderSelectItem[]) {
				state.orderSelectList = orders.length > 0 ? orders : [
					{
						id: MUtils.generateUUID(),
						selectedOrderNo: undefined,
						invoiceAmount: 0
					}
				];
				methods.emitChange();
				methods.emitAmountChange();
			}
		};

		// 监听订单选择列表变化
		watch(
			() => state.orderSelectList,
			() => {
				methods.emitAmountChange();
			},
			{ deep: true }
		);

		// 暴露方法给父组件
		const expose = {
			getSelectedOrders: methods.getSelectedOrders,
			reset: methods.reset,
			setOrderSelectList: methods.setOrderSelectList
		};

		return {
			...toRefs(state),
			...computeds,
			...methods,
			...expose
		};
	}
});
