<template>
	<div class="app-container invoice-management">
		<pm-effi :controller="controller">
			<pm-search>
				<pm-search-single
					:span="8"
					label="开票时间"
					:antdProps="{ placeholder: ['开始时间', '结束时间'], allowClear: true }"
					data-index="applyTimeGte|applyTimeLt"
					xtype="RANGEPICKER"
				/>
			</pm-search>

			<pm-table
				:columns="InvoiceRecordColumns"
				:useCustomColumn="false"
				:helpIcon="false"
				:sort-num-fixed="true"
				:scroll="{ x: 'max-content' }"
				:operations-width="170"
			>
				<template #operations="{ record }">
					<m-button
						@click="handleOpenInvoice(record)"
						type="link"
						:disabled="record.invoiceStatus !== InvoiceRecordStatusEnum.INVOICE_SUCCESS"
					>
						预览发票
					</m-button>

					<m-button
						@click="handleDownLoad(record)"
						type="link"
						:disabled="record.invoiceStatus !== InvoiceRecordStatusEnum.INVOICE_SUCCESS"
					>
						下载发票
					</m-button>
				</template>
			</pm-table>
		</pm-effi>

		<InvoicePreviewComp ref="invoicePreviewRef" />
	</div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
