import { defineComponent, reactive, ref, toRefs } from 'vue';
import { MESSAGE_TYPE, ModelController, MUtils } from '@paas/paas-library';

import { useUserProfileStore } from '@/pinia/user-profile';

import { GetApplyInvoicePreviewStore, GetInvoiceableOrdersListStore, InvoiceStatusOptionsStore } from '../../store';
import { InvoiceTableColumns } from '../../config';
import InvoiceApplicationDialogComp from '../Invoice-application-dialog/index.vue';
import { ConsumptionRecordModel } from '../../types';
import PaymentAgreementComp from '@/components/payment-agreement/index.vue';
import { useAgreementViewStore } from '@/utils/query';
import PaymentAgreementDialogComp from '@/components/payment-agreement-dialog/index.vue';
import { INVOICE_STATUS_OPTIONS } from '@/application/invoice-management/constants';

export default defineComponent({
	name: 'InvoiceAbleOrders',
	components: {
		InvoiceApplicationDialogComp,
		PaymentAgreementComp,
		PaymentAgreementDialogComp
	},
	setup() {
		const userProfileStore = useUserProfileStore();
		const agreementViewStore = useAgreementViewStore();

		const state = reactive({
			selectedRowKeys: [] as string[]
		});

		const controller = new ModelController({
			table: {
				store: GetInvoiceableOrdersListStore
			},
			search: {
				invoiceStatus: {
					store: InvoiceStatusOptionsStore
				}
			}
		});

		const constants = {
			InvoiceTableColumns
		};

		const components = {
			invoiceApplicationDialogRef: ref<InstanceType<typeof InvoiceApplicationDialogComp>>(),
			paymentAgreementDialogRef: ref<InstanceType<typeof PaymentAgreementDialogComp>>(null)
		};

		controller.table.onResponse.use(responseData => {
			const selectedKes = state.selectedRowKeys;

			if (selectedKes.length) {
				responseData.data.itemList.map((item: ConsumptionRecordModel) => {
					const canCheck = item.enableApplyBlueInvoice === true;

					const index = selectedKes.indexOf(item.orderNo);

					if (!canCheck && index !== -1) {
						state.selectedRowKeys.splice(index, 1);
					}
				});
			}

			return responseData;
		});

		const methods = {
			init() {
				userProfileStore.fetchUserProfile();
			},

			onSelectChange(selectedRowKeys: string[]) {
				state.selectedRowKeys = selectedRowKeys;
			},

			getCheckboxProps(record) {
				return {
					disabled: record.enableApplyBlueInvoice !== true
				};
			},

			onSign() {
				components.paymentAgreementDialogRef.value.open();
			},

			async checkSign() {
				if (agreementViewStore.data.value?.signed) {
					return true;
				}

				const bool = await MUtils.confirm({
					title: '提示',
					content: '您的代付款协议未签署，请先签署代付款协议后，再来提交开票',
					confirmText: '去签署',
					cancelText: '取消'
				});

				if (!bool) {
					return false;
				}

				return await components.paymentAgreementDialogRef.value.open();
			},

			// 申请开票
			async onClickOldOrder() {
				const sign = await methods.checkSign();

				if (!sign) {
					return;
				}

				components.invoiceApplicationDialogRef.value.showOldOrder();
			},

			// 点击去开票按钮
			async onClickMakeInvoice() {
				const sign = await methods.checkSign();

				if (!sign) {
					return;
				}

				if (state.selectedRowKeys.length === 0) {
					MUtils.toast('请选择要开票的消费记录', MESSAGE_TYPE.warning);
					return;
				}

				try {
					// 获取开票信息
					const invoiceBaseInfo = await GetApplyInvoicePreviewStore.request({
						jxId: userProfileStore.data.jiaxiaoId,
						orderNos: state.selectedRowKeys,
						newOrder: true
					}).getData();

					// 打开开票申请弹窗
					components.invoiceApplicationDialogRef.value?.show(state.selectedRowKeys, invoiceBaseInfo);
				} catch (error) {
					// 刷新列表，这个时候可能其他用户操作了开票导致失败，那就刷新列表
					controller.tableRequest();

					state.selectedRowKeys.length = 0;

					console.error('开票申请失败:', error);

					MUtils.toast('开票申请失败，请稍后重试', MESSAGE_TYPE.error);
				}
			},

			onInvoiceSubmitClose() {
				// 刷新列表
				controller.tableRequest();
			},

			// 开票申请提交回调
			onInvoiceSubmitSuccess() {
				// 清空选中状态
				state.selectedRowKeys.length = 0;
			},

			getClass(lineData: ConsumptionRecordModel) {
				const value = lineData.invoiceStatus;

				const textClass = INVOICE_STATUS_OPTIONS.find(option => option.key === value)?.textClass;

				return textClass || '';
			}
		};

		methods.init();

		return {
			agreementViewStore,
			controller,
			...components,
			...toRefs(state),
			...methods,
			...constants
		};
	}
});
