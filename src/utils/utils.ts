import dayjs from 'dayjs';

/**
 *    复制到剪切板
 * @content 内容
 */
export function copy2Clipboard(content: string, callback?: () => void): void {
	const input = document.createElement('textarea');
	input.value = content;
	document.body.appendChild(input);
	input.select();
	document.execCommand('copy');
	if (callback && typeof callback === 'function') {
		callback();
	}

	document.body.removeChild(input);
}

// 时间格式化
export function timeFilter(time: number, format = 'YYYY-MM-DD HH:mm:ss'): string {
	if (!time) {
		return '';
	}
	return dayjs(time).format(format);
}

//  加
export const plus = (num1, num2) => {
	let r1, r2;
	try {
		r1 = num1.toString().split('.')[1].length;
	} catch (e) {
		r1 = 0;
	}
	try {
		r2 = num2.toString().split('.')[1].length;
	} catch (e) {
		r2 = 0;
	}
	const m = Math.pow(10, Math.max(r1, r2));
	return (num1 * m + num2 * m) / m;
};

// 减
export const subtract = (num1, num2) => {
	let r1, r2;
	try {
		r1 = num1.toString().split('.')[1].length;
	} catch (e) {
		r1 = 0;
	}
	try {
		r2 = num2.toString().split('.')[1].length;
	} catch (e) {
		r2 = 0;
	}
	const m = Math.pow(10, Math.max(r1, r2));
	const n = r1 >= r2 ? r1 : r2;
	return ((num1 * m - num2 * m) / m).toFixed(n);
};

// 乘
export const multiply = (num1, num2) => {
	num1 = num1 || '';
	num2 = num2 || '';

	let m = 0;
	const s1 = num1.toString();
	const s2 = num2.toString();
	try {
		m += s1.split('.')[1].length;
	} catch (e) {
		// console.log(e);
	}
	try {
		m += s2.split('.')[1].length;
	} catch (e) {
		// console.log(e);
	}
	return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
};

// 除
export const divide = (num1, num2) => {
	let t1 = 0,
		t2 = 0,
		r1,
		r2;
	try {
		t1 = num1.toString().split('.')[1].length;
	} catch (e) {
		console.log(e);
	}
	try {
		t2 = num2.toString().split('.')[1].length;
	} catch (e) {
		console.log(e);
	}

	return (function () {
		r1 = Number(num1.toString().replace('.', ''));
		r2 = Number(num2.toString().replace('.', ''));
		return multiply(r1 / r2, Math.pow(10, t2 - t1)); // multiply乘法配合一起使用
	})();
};
